package com.unity3d.player;

import android.content.Context;
import android.os.Environment;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.TimeUnit;

import androidx.annotation.NonNull;
import androidx.work.Data;
import androidx.work.Worker;
import androidx.work.WorkerParameters;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * WorkManager Worker for downloading APK files with retry mechanism
 */
public class DownloadWorker extends Worker {
    
    public static final String KEY_DOWNLOAD_URL = "download_url";
    public static final String KEY_FILE_NAME = "file_name";
    public static final String KEY_PROGRESS = "progress";
    public static final String KEY_ERROR_MESSAGE = "error_message";
    public static final String KEY_FILE_PATH = "file_path";
    
    private static final int BUFFER_SIZE = 8192;
    private static final int CONNECT_TIMEOUT = 30; // seconds
    private static final int READ_TIMEOUT = 60; // seconds
    
    private OkHttpClient httpClient;
    
    public DownloadWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
        initHttpClient();
    }
    
    private void initHttpClient() {
        httpClient = new OkHttpClient.Builder()
                .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }
    
    @NonNull
    @Override
    public Result doWork() {
        String downloadUrl = getInputData().getString(KEY_DOWNLOAD_URL);
        String fileName = getInputData().getString(KEY_FILE_NAME);
        
        if (downloadUrl == null || fileName == null) {
            return Result.failure(createErrorData("Invalid download parameters"));
        }
        
        try {
            return downloadFile(downloadUrl, fileName);
        } catch (Exception e) {
            return Result.retry(); // Let WorkManager handle retry with exponential backoff
        }
    }
    
    private Result downloadFile(String downloadUrl, String fileName) {
        Request request = new Request.Builder()
                .url(downloadUrl)
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                return Result.failure(createErrorData("Download failed: HTTP " + response.code()));
            }
            
            ResponseBody body = response.body();
            if (body == null) {
                return Result.failure(createErrorData("Empty response body"));
            }
            
            return saveFile(body, fileName);
            
        } catch (IOException e) {
            return Result.failure(createErrorData("Network error: " + e.getMessage()));
        }
    }
    
    private Result saveFile(ResponseBody body, String fileName) {
        File downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
        if (!downloadDir.exists()) {
            downloadDir.mkdirs();
        }

        File file = new File(downloadDir, fileName);

        // 如果文件已存在，先删除它以避免EEXIST错误
        if (file.exists()) {
            if (!file.delete()) {
                return Result.failure(createErrorData("Cannot delete existing file: " + file.getAbsolutePath()));
            }
        }

        long totalBytes = body.contentLength();
        long downloadedBytes = 0;
        
        try (InputStream inputStream = body.byteStream();
             FileOutputStream outputStream = new FileOutputStream(file)) {
            
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
                downloadedBytes += bytesRead;
                
                // Update progress
                if (totalBytes > 0) {
                    int progress = (int) ((downloadedBytes * 100) / totalBytes);
                    setProgressAsync(createProgressData(progress));
                }
                
                // Check if work is cancelled
                if (isStopped()) {
                    file.delete(); // Clean up partial download
                    return Result.failure(createErrorData("Download cancelled"));
                }
            }
            
            // Download completed successfully
            Data outputData = new Data.Builder()
                    .putString(KEY_FILE_PATH, file.getAbsolutePath())
                    .putInt(KEY_PROGRESS, 100)
                    .build();
            
            return Result.success(outputData);
            
        } catch (IOException e) {
            // Clean up partial download
            if (file.exists()) {
                file.delete();
            }
            String errorMsg = "File write error: " + e.getMessage();
            // 添加更详细的错误信息用于调试
            if (e.getMessage() != null && e.getMessage().contains("EEXIST")) {
                errorMsg += " (File already exists and cannot be overwritten)";
            }
            return Result.failure(createErrorData(errorMsg));
        }
    }
    
    private Data createProgressData(int progress) {
        return new Data.Builder()
                .putInt(KEY_PROGRESS, progress)
                .build();
    }
    
    private Data createErrorData(String errorMessage) {
        return new Data.Builder()
                .putString(KEY_ERROR_MESSAGE, errorMessage)
                .build();
    }
}
