1<?xml version="1.0" encoding="utf-8"?>
2<!-- GENERATED BY UNITY. REMOVE THIS COMMENT TO PREVENT OVERWRITING WHEN EXPORTING AGAIN -->
3<manifest xmlns:android="http://schemas.android.com/apk/res/android"
4    package="com.cy.exercisetv"
5    android:installLocation="preferExternal"
6    android:versionCode="31"
7    android:versionName="1.3.2" >
8
9    <uses-sdk
10        android:minSdkVersion="21"
10-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml
11        android:targetSdkVersion="30" />
11-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml
12
13    <supports-screens
13-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:4:3-163
14        android:anyDensity="true"
14-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:4:135-160
15        android:largeScreens="true"
15-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:4:78-105
16        android:normalScreens="true"
16-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:4:49-77
17        android:smallScreens="true"
17-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:4:21-48
18        android:xlargeScreens="true" />
18-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:4:106-134
19
20    <uses-feature android:glEsVersion="0x00030000" />
20-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-54
20-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:19-51
21    <uses-feature
21-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-14:36
22        android:name="android.hardware.vulkan.version"
22-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-55
23        android:required="false" />
23-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-33
24    <uses-feature
24-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:5-17:36
25        android:name="android.hardware.camera"
25-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-47
26        android:required="false" />
26-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:9-33
27    <uses-feature
27-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:5-20:36
28        android:name="android.hardware.camera.autofocus"
28-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-57
29        android:required="false" />
29-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:9-33
30    <uses-feature
30-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:5-23:36
31        android:name="android.hardware.camera.front"
31-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:9-53
32        android:required="false" />
32-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:9-33
33    <uses-feature
33-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:5-26:36
34        android:name="android.hardware.touchscreen"
34-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:9-52
35        android:required="false" />
35-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33
36    <uses-feature
36-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:27:5-29:36
37        android:name="android.hardware.touchscreen.multitouch"
37-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-63
38        android:required="false" />
38-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:9-33
39    <uses-feature
39-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:5-32:36
40        android:name="android.hardware.touchscreen.multitouch.distinct"
40-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:9-72
41        android:required="false" />
41-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:32:9-33
42
43    <uses-permission android:name="android.permission.INTERNET" />
43-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:5-67
43-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:22-64
44    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
44-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:5-81
44-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:22-78
45    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
45-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:5-83
45-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:22-80
46    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
46-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:5-79
46-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:22-76
47    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
47-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:38:5-76
47-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:38:22-73
48    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
48-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:5-75
48-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:22-72
49    <uses-permission android:name="android.permission.CAMERA" />
49-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:5-65
49-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:22-62
50    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
50-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:5-76
50-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:22-73
51    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
51-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:5-79
51-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:22-76
52    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
52-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:5-81
52-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:22-78
53    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
53-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:5-79
53-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:22-76
54    <uses-permission android:name="android.permission.WAKE_LOCK" />
54-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:25:5-68
54-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:25:22-65
55    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
55-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:27:5-81
55-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:27:22-78
56    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
56-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:28:5-77
56-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:28:22-74
57    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
57-->[com.umeng.umsdk:common:9.8.6] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\84233d18c170e846d5dd10a4684392dc\jetified-common-9.8.6\AndroidManifest.xml:11:5-79
57-->[com.umeng.umsdk:common:9.8.6] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\84233d18c170e846d5dd10a4684392dc\jetified-common-9.8.6\AndroidManifest.xml:11:22-76
58
59    <application
59-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:5:3-83
60        android:name="com.unity3d.player.App"
60-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:46:18-55
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.2.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\faae5e4478c326f3f4fa86a104d30d9e\core-1.2.0\AndroidManifest.xml:24:18-86
62        android:debuggable="true"
63        android:icon="@mipmap/app_icon"
63-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:5:49-80
64        android:label="@string/app_name"
64-->E:\huoli-rk3566\hl071501\launcher\src\main\AndroidManifest.xml:5:16-48
65        android:testOnly="true" >
66        <activity
66-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:47:9-74:20
67            android:name="com.unity3d.player.UnityPlayerActivity"
67-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:48:13-66
68            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
68-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:49:13-194
69            android:exported="true"
69-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:50:13-36
70            android:hardwareAccelerated="false"
70-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:51:13-48
71            android:launchMode="singleTask"
71-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:52:13-44
72            android:resizeableActivity="false"
72-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:53:13-47
73            android:screenOrientation="landscape"
73-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:54:13-50
74            android:theme="@style/UnityThemeSelector" >
74-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:55:13-54
75            <intent-filter android:priority="1000" >
75-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:56:13-62:29
75-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:56:28-51
76                <action android:name="android.intent.action.MAIN" />
76-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:57:17-69
76-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:57:25-66
77
78                <category android:name="android.intent.category.LAUNCHER" />
78-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:59:17-77
78-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:59:27-74
79                <category android:name="android.intent.category.HOME" />
79-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:60:17-73
79-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:60:27-70
80                <category android:name="android.intent.category.DEFAULT" />
80-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:61:17-76
80-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:61:27-73
81            </intent-filter>
82            <!-- <intent-filter> -->
83            <!-- <action android:name="android.intent.action.MAIN" /> -->
84            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
85            <!-- </intent-filter> -->
86
87            <meta-data
87-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:68:13-70:40
88                android:name="unityplayer.UnityActivity"
88-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:69:17-57
89                android:value="true" />
89-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:70:17-37
90            <meta-data
90-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:71:13-73:40
91                android:name="android.notch_support"
91-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:72:17-53
92                android:value="true" />
92-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:73:17-37
93        </activity>
94
95        <meta-data
95-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:76:9-78:33
96            android:name="unity.splash-mode"
96-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:77:13-45
97            android:value="0" />
97-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:78:13-30
98        <meta-data
98-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:79:9-81:36
99            android:name="unity.splash-enable"
99-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:80:13-47
100            android:value="True" />
100-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:81:13-33
101        <meta-data
101-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:82:9-84:37
102            android:name="unity.allow-resizable-window"
102-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:83:13-56
103            android:value="False" />
103-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:84:13-34
104        <meta-data
104-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:85:9-87:50
105            android:name="notch.config"
105-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:86:13-40
106            android:value="portrait|landscape" />
106-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:87:13-47
107        <meta-data
107-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:88:9-90:64
108            android:name="TEST_DEVICE_ID"
108-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:89:13-42
109            android:value="2A27406B9CA8A5472970D77E263F014F" />
109-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:90:13-61
110        <meta-data
110-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:91:9-93:68
111            android:name="unity.build-id"
111-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:92:13-42
112            android:value="3e5cc2c2-84b1-41e0-ae5f-809a2a5eda1f" />
112-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:93:13-65
113        <meta-data
113-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:94:9-96:56
114            android:name="UMENG_APPKEY"
114-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:95:13-40
115            android:value="686a18c779267e0210a1cb03" />
115-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:96:13-53
116        <meta-data
116-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:97:9-99:38
117            android:name="UMENG_CHANNEL"
117-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:98:13-41
118            android:value="uemeng" />
118-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:99:13-35
119
120        <provider
120-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:101:9-109:20
121            android:name="androidx.core.content.FileProvider"
121-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:102:13-62
122            android:authorities="com.cy.exercisetv.fileprovider"
122-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:103:13-64
123            android:exported="false"
123-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:104:13-37
124            android:grantUriPermissions="true" >
124-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:105:13-47
125            <meta-data
125-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:106:13-108:54
126                android:name="android.support.FILE_PROVIDER_PATHS"
126-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:107:17-67
127                android:resource="@xml/file_paths" />
127-->[:unityLibrary] E:\huoli-rk3566\hl071501\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:108:17-51
128        </provider>
129        <provider
129-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:31:9-39:20
130            android:name="androidx.startup.InitializationProvider"
130-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:32:13-67
131            android:authorities="com.cy.exercisetv.androidx-startup"
131-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:33:13-68
132            android:exported="false" >
132-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:34:13-37
133            <meta-data
133-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:36:13-38:52
134                android:name="androidx.work.WorkManagerInitializer"
134-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:37:17-68
135                android:value="androidx.startup" />
135-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:38:17-49
136        </provider>
137
138        <service
138-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:41:9-46:35
139            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
139-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:42:13-88
140            android:directBootAware="false"
140-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:43:13-44
141            android:enabled="@bool/enable_system_alarm_service_default"
141-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:44:13-72
142            android:exported="false" />
142-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:45:13-37
143        <service
143-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:47:9-53:35
144            android:name="androidx.work.impl.background.systemjob.SystemJobService"
144-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:48:13-84
145            android:directBootAware="false"
145-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:49:13-44
146            android:enabled="@bool/enable_system_job_service_default"
146-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:50:13-70
147            android:exported="true"
147-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:51:13-36
148            android:permission="android.permission.BIND_JOB_SERVICE" />
148-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:52:13-69
149        <service
149-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:54:9-59:35
150            android:name="androidx.work.impl.foreground.SystemForegroundService"
150-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:55:13-81
151            android:directBootAware="false"
151-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:56:13-44
152            android:enabled="@bool/enable_system_foreground_service_default"
152-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:57:13-77
153            android:exported="false" />
153-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:58:13-37
154
155        <receiver
155-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:61:9-66:35
156            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
156-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:62:13-88
157            android:directBootAware="false"
157-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:63:13-44
158            android:enabled="true"
158-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:64:13-35
159            android:exported="false" />
159-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:65:13-37
160        <receiver
160-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:67:9-77:20
161            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
161-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:68:13-106
162            android:directBootAware="false"
162-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:69:13-44
163            android:enabled="false"
163-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:70:13-36
164            android:exported="false" >
164-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:71:13-37
165            <intent-filter>
165-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:73:13-76:29
166                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
166-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:74:17-87
166-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:74:25-84
167                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
167-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:75:17-90
167-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:75:25-87
168            </intent-filter>
169        </receiver>
170        <receiver
170-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:78:9-88:20
171            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
171-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:79:13-104
172            android:directBootAware="false"
172-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:80:13-44
173            android:enabled="false"
173-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:81:13-36
174            android:exported="false" >
174-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:82:13-37
175            <intent-filter>
175-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:84:13-87:29
176                <action android:name="android.intent.action.BATTERY_OKAY" />
176-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:85:17-77
176-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:85:25-74
177                <action android:name="android.intent.action.BATTERY_LOW" />
177-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:86:17-76
177-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:86:25-73
178            </intent-filter>
179        </receiver>
180        <receiver
180-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:89:9-99:20
181            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
181-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:90:13-104
182            android:directBootAware="false"
182-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:91:13-44
183            android:enabled="false"
183-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:92:13-36
184            android:exported="false" >
184-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:93:13-37
185            <intent-filter>
185-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:95:13-98:29
186                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
186-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:96:17-83
186-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:96:25-80
187                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
187-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:97:17-82
187-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:97:25-79
188            </intent-filter>
189        </receiver>
190        <receiver
190-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:100:9-109:20
191            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
191-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:101:13-103
192            android:directBootAware="false"
192-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:102:13-44
193            android:enabled="false"
193-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:103:13-36
194            android:exported="false" >
194-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:104:13-37
195            <intent-filter>
195-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:106:13-108:29
196                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
196-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:107:17-79
196-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:107:25-76
197            </intent-filter>
198        </receiver>
199        <receiver
199-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:110:9-121:20
200            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
200-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:111:13-88
201            android:directBootAware="false"
201-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:112:13-44
202            android:enabled="false"
202-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:113:13-36
203            android:exported="false" >
203-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:114:13-37
204            <intent-filter>
204-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:116:13-120:29
205                <action android:name="android.intent.action.BOOT_COMPLETED" />
205-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:117:17-79
205-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:117:25-76
206                <action android:name="android.intent.action.TIME_SET" />
206-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:118:17-73
206-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:118:25-70
207                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
207-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:119:17-81
207-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:119:25-78
208            </intent-filter>
209        </receiver>
210        <receiver
210-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:122:9-131:20
211            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
211-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:123:13-99
212            android:directBootAware="false"
212-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:124:13-44
213            android:enabled="@bool/enable_system_alarm_service_default"
213-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:125:13-72
214            android:exported="false" >
214-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:126:13-37
215            <intent-filter>
215-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:128:13-130:29
216                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
216-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:129:17-98
216-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:129:25-95
217            </intent-filter>
218        </receiver>
219        <receiver
219-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:132:9-142:20
220            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
220-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:133:13-78
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:134:13-44
222            android:enabled="true"
222-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:135:13-35
223            android:exported="true"
223-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:136:13-36
224            android:permission="android.permission.DUMP" >
224-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:137:13-57
225            <intent-filter>
225-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:139:13-141:29
226                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
226-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:140:17-88
226-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:140:25-85
227            </intent-filter>
228        </receiver>
229
230        <service
230-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:10:9-14:41
231            android:name="com.efs.sdk.memleaksdk.monitor.UMonitorService"
231-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:11:13-74
232            android:enabled="true"
232-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:12:13-35
233            android:exported="false"
233-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:13:13-37
234            android:process=":u_heap" />
234-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:14:13-38
235        <service
235-->[androidx.room:room-runtime:2.2.5] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\7cdeb1d41cb3439f6c770ab9c6dbd234\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
236            android:name="androidx.room.MultiInstanceInvalidationService"
236-->[androidx.room:room-runtime:2.2.5] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\7cdeb1d41cb3439f6c770ab9c6dbd234\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
237            android:directBootAware="true"
237-->[androidx.room:room-runtime:2.2.5] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\7cdeb1d41cb3439f6c770ab9c6dbd234\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
238            android:exported="false" />
238-->[androidx.room:room-runtime:2.2.5] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\7cdeb1d41cb3439f6c770ab9c6dbd234\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
239    </application>
240
241</manifest>
